<template>
  <div class="consultas-container">
    <div v-if="isLoading" class="w-100 text-center py-3">
      <div class="spinner-border text-primary" role="status"></div>
    </div>

    <div v-else-if="!paciente.consultas || paciente.consultas.length === 0" class="empty-state">
      <div class="empty-state-message">
        <div class="icon-wrapper">
          <font-awesome-icon :icon="['fas', 'calendar-times']" class="empty-state-icon" />
        </div>
        <p>Não há consultas registradas para este paciente.</p>
      </div>
    </div>

    <div v-else>
      <EasyDataTable
        :headers="headers"
        :items="paciente.consultas"
        :rows-per-page="5"
        :rows-items="[5, 10, 15]"
        body-row-class-name="consulta-row"
        header-item-class-name="table-header-item"
        body-item-class-name="table-body-item"
        rows-per-page-message="Consultas por página"
        rows-of-page-separator-message="de"
        empty-message="Sem consultas registradas"
      >
        <template #header-dentista_id>
          <div class="w-100">ORTODONTISTA</div>
        </template>

        <template #header-categoria>
          <div class="text-center w-100">CATEGORIA</div>
        </template>

        <template #header-valor>
          <div class="text-center w-100">VALOR</div>
        </template>

        <template #header-status>
          <div class="text-center w-100">STATUS</div>
        </template>

        <template #header-acoes>
          <div class="text-center w-100">AÇÕES</div>
        </template>

        <template #item-horario="{ horario }">
          <div class="d-flex flex-column justify-content-center">
            <p class="text-xs font-weight-bold mb-0">
              {{ $filters.dateDmy(horario) }}
            </p>
            <p class="text-xs mb-0">
              <b>{{ $filters.howMuchTime(horario, { type: 'date' }) }}</b>
            </p>
          </div>
        </template>

        <template #item-categoria="{ categoria }">
          <div class="d-flex justify-content-center">
            <span class="categoria-badge" :class="getCategoriaClass(categoria)">
              <font-awesome-icon :icon="['fas', getCategoriaIcon(categoria)]" class="me-1" />
              {{ getCategoriaNome(categoria) }}
            </span>
          </div>
        </template>

        <template #item-dentista_id="{ dentista_id }">
          <div class="d-flex flex-column justify-content-center">
            <p class="text-xs font-weight-bold mb-0 dentista-name">
              {{ getDentistaName(dentista_id) }}
            </p>
          </div>
        </template>

        <template #item-valor="{ valor }">
          <div class="d-flex justify-content-center">
            <span v-if="valor && valor !== 0" class="valor-badge">
              {{ formatCurrency(valor) }}
            </span>
            <span v-else class="text-muted">-</span>
          </div>
        </template>

        <template #item-status="{ status }">
          <div class="d-flex justify-content-center">
            <span class="badge" :class="getStatusClass(status)">
              {{ getStatusText(status) }}
            </span>
          </div>
        </template>

        <template #item-acoes="{ id }">
          <div class="d-flex justify-content-center">
            <button
              class="btn btn-sm btn-outline-info"
              @click.stop="verHistorico(id)"
              title="Ver histórico"
            >
              <font-awesome-icon :icon="['fas', 'history']" class="me-2" />
              Histórico
            </button>
          </div>
        </template>
      </EasyDataTable>
    </div>
  </div>
</template>

<script>
import { getConsultasByPaciente } from "@/services/consultasService";
import cSwal from "@/utils/cSwal.js";

export default {
  name: "ConsultasTable",
  props: {
    paciente: {
      type: Object,
      required: true
    },
    dentistas: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:consultas', 'ver-historico'],
  data() {
    return {
      isLoading: false,
      headers: [
        { text: "DATA", value: "horario", sortable: true },
        { text: "CATEGORIA", value: "categoria", sortable: true, align: "center" },
        { text: "ORTODONTISTA", value: "dentista_id", sortable: true },
        { text: "VALOR", value: "valor", sortable: true, align: "center" },
        { text: "STATUS", value: "status", sortable: true, align: "center" },
        { text: "AÇÕES", value: "acoes", sortable: false, align: "center" }
      ],
      categorias: [
        { valor: 'acompanhamento', nome: 'Acompanhamento/ativação', cor: 'info', icone: 'wrench' },
        { valor: 'primeira_consulta', nome: 'Primeira consulta', cor: 'success', icone: 'clipboard-check' },
        { valor: 'emergencia', nome: 'Emergência', cor: 'danger', icone: 'exclamation-triangle' },
        { valor: 'montagem', nome: 'Montagem', cor: 'secondary', icone: 'tools' },
        { valor: 'remocao', nome: 'Remoção', cor: 'secondary', icone: 'minus-circle' },
        { valor: 'replanejamento', nome: 'Replanejamento', cor: 'dark', icone: 'sync-alt' },
        { valor: 'pos_tratamento', nome: 'Pós-tratamento', cor: 'secondary', icone: 'check-double' }
      ]
    };
  },
  computed: {
  },
  mounted() {
    // If the paciente object doesn't have consultas property or it's empty,
    // we can try to fetch them from the API
    if (!this.paciente.consultas || this.paciente.consultas.length === 0) {
      this.fetchConsultas();
    }
  },
  methods: {
    async fetchConsultas() {
      if (!this.paciente || !this.paciente.id) return;

      this.isLoading = true;
      try {
        const consultas = await getConsultasByPaciente(this.paciente.id);
        if (consultas && Array.isArray(consultas)) {
          // In Vue 3, we can directly assign to the property
          // The component will receive the updated data through props
          this.$emit('update:consultas', consultas);
        }
      } catch (error) {
        console.error('Erro ao buscar consultas do paciente:', error);
        cSwal.cError("Erro ao carregar consultas do paciente.");
      } finally {
        this.isLoading = false;
      }
    },
    getDentistaName(dentistaId) {
      if (!dentistaId) return '-';

      const dentista = this.dentistas.find(d => d.id === dentistaId || d.id === parseInt(dentistaId));
      return dentista ? dentista.nome : `Dentista #${dentistaId}`;
    },
    formatCurrency(value) {
      if (!value && value !== 0) return '-';

      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    },
    getStatusClass(status) {
      const statusClasses = {
        'agendada': 'bg-info',
        'realizada': 'bg-success',
        'cancelada': 'bg-danger',
        'reagendada': 'bg-warning'
      };

      return statusClasses[status] || 'bg-secondary';
    },
    getStatusText(status) {
      const statusTexts = {
        'agendada': 'AGENDADA',
        'realizada': 'REALIZADA',
        'cancelada': 'CANCELADA',
        'reagendada': 'REAGENDADA'
      };

      return statusTexts[status] || status.toUpperCase();
    },
    getCategoriaData(categoria) {
      return this.categorias.find(cat => cat.valor === categoria) ||
             { valor: categoria, nome: categoria, cor: 'secondary', icone: 'question' };
    },
    getCategoriaNome(categoria) {
      if (!categoria) return '-';
      return this.getCategoriaData(categoria).nome;
    },
    getCategoriaIcon(categoria) {
      if (!categoria) return 'question';
      return this.getCategoriaData(categoria).icone;
    },
    getCategoriaClass(categoria) {
      if (!categoria) return 'categoria-badge-secondary';
      const cor = this.getCategoriaData(categoria).cor;
      return `categoria-badge-${cor}`;
    },
    verHistorico(id) {
      // Emitir evento para o componente pai abrir o modal de histórico
      this.$emit('ver-historico', id);
    }
  }
};
</script>

<style scoped>
.consultas-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.empty-state-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.5rem 2rem;
  text-align: center;
  background: #fff;
  border-radius: 16px;
  margin: 0.5rem 0;
  border: 2px dashed #e2e8f0;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.empty-state-message:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.1);
}

.empty-state-message .icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.empty-state-message .empty-state-icon {
  font-size: 2rem;
  color: #fff;
}

.empty-state-message p {
  color: #64748b;
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
  line-height: 1.5;
}

/* Status badges */
.badge {
  font-size: 0.75rem;
  padding: 0.5rem 1rem;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-radius: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Categoria badges */
.categoria-badge {
  display: inline-flex;
  align-items: center;
  font-size: 0.65rem;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.categoria-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.categoria-badge-info {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: #fff;
}

.categoria-badge-success {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: #fff;
}

.categoria-badge-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: #fff;
}

.categoria-badge-secondary {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: #fff;
}

.categoria-badge-dark {
  background: linear-gradient(135deg, #343a40, #1d2124);
  color: #fff;
}

/* Valor badge */
.valor-badge {
  display: inline-flex;
  align-items: center;
  font-size: 0.75rem;
  padding: 0.3rem 0.7rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #495057;
  border-radius: 10px;
  font-weight: 700;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.valor-badge:hover {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Dentista name styling */
.dentista-name {
  color: #495057 !important;
  font-weight: 600 !important;
  font-size: 0.85rem !important;
}

/* Table row styling */
.consulta-row {
  cursor: default;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.consulta-row:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.03), rgba(118, 75, 162, 0.03)) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

/* Table header styling */
.table-header-item {
  font-size: 0.75rem !important;
  font-weight: 700 !important;
  color: #344767 !important;
  text-transform: uppercase !important;
  padding: 1rem 1.5rem !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  border-bottom: 2px solid #e2e8f0 !important;
  letter-spacing: 0.5px;
}

.table-body-item {
  font-size: 0.875rem !important;
  color: #64748b !important;
  padding: 1rem 1.5rem !important;
  vertical-align: middle !important;
}

/* Action buttons */
.btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.btn-outline-primary {
  border-color: #007bff;
  color: #007bff;
  background: transparent;
}

.btn-outline-primary:hover {
  background: #007bff;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-outline-info {
  border-color: #17a2b8;
  color: #17a2b8;
  background: transparent;
}

.btn-outline-info:hover {
  background: #17a2b8;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

/* Gap between action buttons */
.gap-2 {
  gap: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .categoria-badge {
    font-size: 0.6rem;
    padding: 0.25rem 0.5rem;
  }

  .valor-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.6rem;
  }

  .table-header-item {
    padding: 0.75rem 1rem !important;
    font-size: 0.7rem !important;
  }

  .table-body-item {
    padding: 0.75rem 1rem !important;
    font-size: 0.8rem !important;
  }

  .btn-sm {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
  }
}

/* Loading spinner styling */
.spinner-border {
  color: #667eea;
}

/* Custom scrollbar for table */
:deep(.vue3-easy-data-table__main) {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

:deep(.vue3-easy-data-table__main::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

:deep(.vue3-easy-data-table__main::-webkit-scrollbar-track) {
  background: #f7fafc;
  border-radius: 4px;
}

:deep(.vue3-easy-data-table__main::-webkit-scrollbar-thumb) {
  background: #cbd5e0;
  border-radius: 4px;
}

:deep(.vue3-easy-data-table__main::-webkit-scrollbar-thumb:hover) {
  background: #a0aec0;
}
</style>
