<template>
  <div class="tratamento-content">
    <div class="row">
      <div class="col-md-12">
        <div class="box primary h-100">
          <p class="custom-card-header text-center">
            Diagnóstico
          </p>

          <div class="diagnostico-container">
            <div class="row">
              <div class="col-12 col-lg-6">
                <div class="diagnostico-items-grid">
                  <div v-for="(fator, index) in paciente.fatores_diagnostico" v-bind:key="index" class="diagnostico-item-wrapper">
                    <div class="diagnostico-item" :class="fator.nivel">
                      <div class="diagnostico-icon">
                        <font-awesome-icon :icon="['fas', 'info-circle']" />
                      </div>
                      <div class="diagnostico-content">
                        <span>{{ fator.fator_diagnostico }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12 col-lg-6">
                <div class="d-block d-lg-none p-horizontal-divider my-3"></div>
                <div class="observacoes-section">
                  <div class="observacoes-header">
                    <font-awesome-icon :icon="['fas', 'clipboard-list']" class="observacoes-icon" />
                    <label class="observacoes-label">Observações Clínicas</label>
                    <span class="edit-icon-wrapper" :class="{ 'active': isEditing['diagnostico'] }" @click="toggleEditMode('diagnostico')">
                      <font-awesome-icon
                        :icon="['fas', 'edit']"
                        class="edit-icon"
                        :title="isEditing['diagnostico'] ? 'Sair do modo de edição' : 'Editar o diagnóstico'"
                      />
                    </span>
                    <span
                      v-if="isEditing.diagnostico"
                      class="text-capitalize text-light pointer ms-2"
                      @click="toggleEditMode('diagnostico')"
                      ><u>Cancelar edição</u></span
                    >
                  </div>
                  <div class="observacoes-container">
                    <div
                      v-if="!isEditing['diagnostico']"
                      class="observacoes-text"
                    >
                      <div
                        v-if="!diagnostico_ || diagnostico_.length == 0"
                        class="observacoes-empty"
                      >
                        <span>Nenhuma observação registrada</span>
                        <small>Clique no ícone de edição para adicionar observações</small>
                      </div>
                      <div v-if="diagnostico_ && diagnostico_.length > 0" class="observacoes-content">
                        {{ diagnostico_ }}
                      </div>
                    </div>

                    <textarea
                      v-if="isEditing['diagnostico']"
                      class="form-control observacoes-textarea"
                      rows="8"
                      v-model="diagnostico_"
                      placeholder="Insira suas observações clínicas sobre o diagnóstico..."
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="isEditing['diagnostico']" class="w-100 text-center mb-3">
            <div class="p-vertical-divider"></div>
            <button
              class="btn btn-sm btn-primary mt-3 mb-0 btn-save"
              title="Salvar as alterações realizadas"
              @click="confirmSalvarDiagnostico"
            >
              <i class="fas fa-save me-2"></i>Salvar
            </button>
          </div>

        </div>
      </div>

      <div class="col-12 mt-4">
        <div class="box primary">
          <p class="custom-card-header text-center">Protocolo de tratamento</p>
          <div
            v-if="
              paciente.tratamentos_sugeridos && paciente.tratamentos_sugeridos.length > 0
            "
            class="row protocolo-content"
          >
            <div class="col-sm-5 tratamento-column">
              <div class="tratamento-recomendado-section">
                <div class="section-header">
                  <font-awesome-icon :icon="['fas', 'stethoscope']" class="section-icon" />
                  <h4 class="section-title">Tratamento Recomendado</h4>
                </div>

                <div class="tratamento-frame">
                  <div class="tratamento-card">
                    <div class="card-header p-0">
                      <div class="option-image-container">
                        <img :src="imagePaths[paciente.tratamentos_sugeridos[0].tag]" class="card-img-top" />
                      </div>
                      <div class="treatment-title p-3">
                        <h5 class="treatment-name">
                          {{ paciente.tratamentos_sugeridos[0].tratamento }}
                        </h5>
                      </div>
                    </div>
                    <div class="card-body p-3">
                      <div class="treatment-description">
                        <p v-if="paciente.tratamentos_sugeridos[0].observacao" class="primary-observation">
                          {{ paciente.tratamentos_sugeridos[0].observacao }}
                        </p>
                        <p v-if="paciente.tratamentos_sugeridos[0].observacao_secundaria" class="secondary-observation">
                          {{ paciente.tratamentos_sugeridos[0].observacao_secundaria }}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="tratamento-badge">
                    <font-awesome-icon :icon="['fas', 'search']" />
                    <span>Análise automática</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-sm-7 informacoes-column align-center">
              <div class="fatores-clinicos-section">
                <div class="fatores-info-card">
                  <div class="fatores-info-header">
                    <font-awesome-icon :icon="['fas', 'info-circle']" class="fatores-info-icon" />
                  </div>
                  <p class="text-center my-0 py-2">
                    A recomendação de
                    <strong class="text-sm uppercase">{{
                      paciente.tratamentos_sugeridos[0].tratamento
                    }}</strong>
                    como tratamento foi baseada nos seguintes fatores clínicos do paciente:
                  </p>
                  <div class="conditions-container-small">
                    <div
                      v-for="fator in fatoresClinicosFiltrados"
                      v-bind:key="fator.fator_clinico"
                      class="condition-small"
                    >
                      <div class="card mx-1 mt-0" style="border: 1px solid #ddd">
                        <div class="card-header p-0">
                          <div class="option-image-container-small">
                            <img :src="imagePaths[fator.tag]" class="card-img-top" />
                          </div>
                        </div>
                        <div class="card-body p-2">
                          <p class="card-text p-0 text-center">
                            <strong>{{ fator.fator_clinico }}</strong>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="col-12">
              <div class="p-horizontal-divider mt-5 mb-4"></div>
              <div class="mentoria-section-bottom">
                <div class="section-header mb-3">
                  <font-awesome-icon :icon="['fas', 'user-md']" class="section-icon" />
                  <h4 class="section-title">Orientação Especializada</h4>
                </div>

                <div class="mentoria-info-card-centered">
                  <div class="mentoria-info">
                    <div class="mentoria-text">
                      <p class="mentoria-description">
                        Se você tiver dúvidas sobre esta recomendação, pode solicitar uma <strong>mentoria especializada</strong> para este caso.
                      </p>
                    </div>
                  </div>

                  <div class="mentoria-action">
                    <button
                      v-if="!mentoriaSolicitada"
                      class="btn-mentoria"
                      data-bs-toggle="modal"
                      data-bs-target="#modalSolicitarMentoria"
                    >
                      <font-awesome-icon :icon="['fas', 'user-doctor']" />
                      <span>SOLICITAR MENTORIA</span>
                    </button>
                    <div
                      v-if="mentoriaSolicitada"
                      class="mentoria-solicitada"
                    >
                      <font-awesome-icon :icon="['fas', 'check-circle']" />
                      <span>Mentoria Solicitada</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </div>

          <div
            v-if="
              !paciente.tratamentos_sugeridos ||
              paciente.tratamentos_sugeridos.length == 0
            "
            class="row border-between py-3"
          >
            <div
              class="col-sm-6 pb-1 px-4 border-end text-center d-flex flex-column justify-content-center align-center"
            >
              <h3>Não foi possível recomendar um tratamento</h3>
              <p class="mt-3">
                A análise realizada não foi suficiente para que pudéssemos recomendar um
                tratamento.
              </p>
            </div>

            <div class="col-sm-6">
              <p class="text-justify my-0 pt-2 pb-2">
                O <b>Lumi Plan</b> calcula o tratamento recomendado, com base nas
                informações inseridas na seção "<a
                  class="link"
                  title='Ir para a seção "análise"'
                  href="#"
                  @click="goToAnalise"
                  ><font-awesome-icon
                    :icon="['fas', 'fa-search']"
                    class="me-1 text-sm"
                  /><span class="text-sm font-weight-bold uppercase">análise</span></a
                >". Porém, com a análise atual, não foi possível definir um tratamento
                para este paciente. Verifique se é possível
                <strong>complementar a análise</strong>.
              </p>

              <div class="p-horizontal-divider m-3"></div>

              <div class="mentoria-section-no-treatment">
                <div class="section-header mb-3">
                  <font-awesome-icon :icon="['fas', 'user-md']" class="section-icon" />
                  <h4 class="section-title">Orientação Especializada</h4>
                </div>

                <div class="mentoria-info-card">
                  <div class="mentoria-info">
                    <div class="mentoria-text">
                      <p class="mentoria-description">
                        Se você tiver dúvidas sobre este caso, pode solicitar uma <strong>mentoria especializada</strong>.
                      </p>
                    </div>
                  </div>

                  <div class="mentoria-action">
                    <button
                      v-if="!mentoriaSolicitada"
                      class="btn-mentoria"
                      data-bs-toggle="modal"
                      data-bs-target="#modalSolicitarMentoria"
                    >
                      <font-awesome-icon :icon="['fas', 'user-doctor']" />
                      <span>SOLICITAR MENTORIA</span>
                    </button>
                    <div
                      v-if="mentoriaSolicitada"
                      class="mentoria-solicitada"
                    >
                      <font-awesome-icon :icon="['fas', 'check-circle']" />
                      <span>Mentoria Solicitada</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-12 mt-4">
        <div class="box primary h-100 prognostico-card">
          <p class="custom-card-header text-center">
            Prognóstico
          </p>
          <div class="prognostico-container">
            <div v-if="!prognostico_ || prognostico_.length === 0" class="prognostico-info">
              Ainda não foi definido um prognóstico para este paciente. <strong>Selecione-o abaixo</strong>:
            </div>

            <div class="prognostico-buttons-container">
              <div class="btn-prognostico-wrapper">
                <div v-if="prognostico_ === 'Desfavorável'" class="prognostico-arrow arrow-desfavoravel"></div>
                <button
                  type="button"
                  class="btn-prognostico btn-desfavoravel"
                  :class="{ active: prognostico_ === 'Desfavorável' }"
                  @click="handlePrognosticoClick('Desfavorável')"
                  title="Desfavorável"
                >
                  <font-awesome-icon :icon="['fas', 'times']" :style="{ fontSize: '24px', opacity: prognostico_ === 'Desfavorável' ? '1' : '0.5' }" />
                  Desfavorável
                </button>
              </div>

              <div class="btn-prognostico-wrapper">
                <div v-if="prognostico_ === 'duvidoso'" class="prognostico-arrow arrow-duvidoso"></div>
                <button
                  type="button"
                  class="btn-prognostico btn-duvidoso"
                  :class="{ active: prognostico_ === 'duvidoso' }"
                  @click="handlePrognosticoClick('duvidoso')"
                  title="duvidoso"
                >
                  <font-awesome-icon :icon="['fas', 'question']" :style="{ fontSize: '24px', opacity: prognostico_ === 'duvidoso' ? '1' : '0.5' }" />
                  duvidoso
                </button>
              </div>

              <div class="btn-prognostico-wrapper">
                <div v-if="prognostico_ === 'Favorável'" class="prognostico-arrow arrow-favoravel"></div>
                <button
                  type="button"
                  class="btn-prognostico btn-favoravel"
                  :class="{ active: prognostico_ === 'Favorável' }"
                  @click="handlePrognosticoClick('Favorável')"
                  title="Favorável"
                >
                  <font-awesome-icon :icon="['fas', 'check']" :style="{ fontSize: '24px', opacity: prognostico_ === 'Favorável' ? '1' : '0.5' }" />
                  Favorável
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-12">
        <div class="p-horizontal-divider"></div>

        <div class="next-btn-container py-2 py-md-3">
          <button class="btn btn-success mb-0" @click="gerarPlanoTratamento">
            <i class="me-2 fas fa-file-pen" style="font-size: 13pt"></i>
            <span style="font-size: 10pt"> GERAR PLANO DE TRATAMENTO </span>
            <i class="ms-2 fas fa-chevron-right" style="font-size: 13pt"></i>
          </button>
        </div>
      </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modalSolicitarMentoria">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content mentoria-modal">
          <div class="modal-header mentoria-header">
            <h5 class="modal-title">Solicitar mentoria</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body mentoria-body py-4">
            <p class="mentoria-paciente">
              Mentoria para o caso do paciente <b>{{ paciente.nome }}</b>
            </p>
            <p class="mentoria-info">
              O pedido de mentoria será enviado aos nossos especialistas, que poderão avaliar o caso junto a você.
            </p>
            <p class="mentoria-label">
              Escreva algumas observações sobre o caso, se julgar necessário:
            </p>
            <textarea
              class="form-control mentoria-textarea"
              rows="4"
              v-model="observacoesMentoria"
              placeholder="Descreva suas dúvidas ou observações sobre o caso..."
            ></textarea>
          </div>
          <div class="modal-footer mentoria-footer">
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
              Cancelar
            </button>
            <button
              type="button"
              class="btn btn-primary mentoria-btn"
              data-bs-dismiss="modal"
              @click="confirmSolicitarMentoria"
            >
              <i class="fas fa-paper-plane me-2"></i>Solicitar mentoria
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.treatment-title {
  font-size: 12pt;
  text-align: center;
  font-weight: 500;
}

.link {
  text-decoration: underline;
  color: #4487d3;
  transition: color 0.2s ease;
}

.link:hover {
  color: #1B4464;
}

/* Estilos para o container de diagnóstico */
.diagnostico-container {
  background: #f8f9fa;
  padding: 1.5rem;
  padding-top: 0.8rem;
  border-radius: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Grid para os itens de diagnóstico */
.diagnostico-items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 12px;
  padding: 1rem 0.5rem;
}

.diagnostico-item-wrapper {
  display: flex;
  justify-content: center;
}

/* Estilos para os itens de diagnóstico - mais compactos */
.diagnostico-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  border-radius: 6px;
  background-color: #F9FAFC;
  border-left: 3px solid #DDD;
  border: 1px solid #E0E5EB;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
  transition: all 0.2s ease;
  font-size: 0.85rem;
  width: 100%;
  max-width: 260px;
}

.diagnostico-item:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.diagnostico-item.positivo {
  border-left: 3px solid #4CAF50;
  background-color: #F1F8F1;
}

.diagnostico-item.neutro {
  border-left: 3px solid #80A1BB;
  background-color: #F5F9FC;
}

.diagnostico-item.atencao {
  border-left: 3px solid #FFC107;
  background-color: #FFFBF0;
}

.diagnostico-item.negativo {
  border-left: 3px solid #F44336;
  background-color: #FEF5F4;
}

.diagnostico-icon {
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 8px;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  font-size: 0.75rem;
}

.positivo .diagnostico-icon {
  color: #4CAF50;
}

.neutro .diagnostico-icon {
  color: #80A1BB;
}

.atencao .diagnostico-icon {
  color: #FFC107;
}

.negativo .diagnostico-icon {
  color: #F44336;
}

.diagnostico-content {
  flex-grow: 1;
  font-size: 0.9rem;
  line-height: 1.4;
  padding-top: 2px;
}

/* Estilos para a seção de observações */
.observacoes-section {
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border-radius: 12px;
  padding: 1.2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #E8ECF0;
}

.observacoes-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #E8ECF0;
}

.observacoes-icon {
  color: #1B4464;
  font-size: 1.1rem;
  margin-right: 8px;
}

.observacoes-label {
  font-weight: 600;
  color: #1B4464;
  margin: 0;
  font-size: 0.95rem;
  letter-spacing: 0.3px;
}

.observacoes-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  border: 1px solid #E8ECF0;
  overflow: hidden;
}

.observacoes-text {
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.observacoes-empty {
  text-align: center;
  color: #6c757d;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.observacoes-empty span {
  font-weight: 500;
  font-size: 0.95rem;
}

.observacoes-empty small {
  font-size: 0.8rem;
  color: #adb5bd;
}

.observacoes-content {
  padding: 1.2rem;
  text-align: justify;
  line-height: 1.6;
  color: #333;
  font-size: 0.9rem;
}

.observacoes-textarea {
  border: none;
  border-radius: 8px;
  padding: 15px;
  font-size: 0.95rem;
  box-shadow: none;
  transition: all 0.3s ease;
  min-height: 80px;
}

.observacoes-textarea:focus {
  box-shadow: 0 0 0 3px rgba(27, 68, 100, 0.1);
  outline: none;
}

/* Estilos para o botão de salvar */
.btn-save {
  padding: 8px 20px;
  font-weight: 500;
  border-radius: 6px;
  box-shadow: 0 3px 6px rgba(27, 68, 100, 0.15);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #1B4464, #56809F);
  border: none;
}

.btn-save:hover {
  box-shadow: 0 4px 8px rgba(27, 68, 100, 0.25);
  transform: translateY(-1px);
  background: linear-gradient(135deg, #28597e, #7aa3c0);
}

/* Estilos para o ícone de edição */
.edit-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  margin-left: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.edit-icon-wrapper:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.edit-icon-wrapper.active {
  background-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}

.edit-icon {
  color: white;
  font-size: 14px;
}

/* Estilos para o protocolo de tratamento */
.protocolo-content {
  padding: 1.5rem;
  padding-top: 0.8rem;
  margin: 0;
}

/* Colunas do protocolo */
.tratamento-column {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
}

.informacoes-column {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  min-height: 500px;
}

.informacoes-column > * {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Seção do tratamento recomendado */
.tratamento-recomendado-section {
  width: 100%;
  max-width: 350px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  gap: 10px;
}

.section-icon {
  color: #1B4464;
  font-size: 1.3rem;
}

.section-title {
  color: #1B4464;
  font-weight: 600;
  margin: 0;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tratamento-frame {
  position: relative;
  background: linear-gradient(145deg, #f8f9fa, #ffffff);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 24px rgba(27, 68, 100, 0.08);
  border: 2px solid #E8ECF0;
  max-width: 320px;
  margin: 0 auto;
}

.tratamento-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid #E8ECF0;
  transition: all 0.3s ease;
}

.tratamento-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.treatment-title {
  background: linear-gradient(135deg, #4A6B85, #7AA3C0);
  text-align: center;
}

.treatment-name {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.treatment-description {
  background: linear-gradient(to bottom, #fafafa, #f2f2f2);
}

.primary-observation {
  font-weight: 600;
  color: #1B4464;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.secondary-observation {
  color: #495057;
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
}

.tratamento-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 3px 8px rgba(40, 167, 69, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* Seção de análise */
.analise-section {
  width: 100%;
}

.analise-info-card {
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border-radius: 12px;
  padding: 1.2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #E8ECF0;
}

.analise-description {
  color: #495057;
  line-height: 1.6;
  margin: 0;
  font-size: 0.9rem;
}

.analise-link {
  color: #1B4464;
  text-decoration: none;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.analise-link:hover {
  background-color: rgba(27, 68, 100, 0.1);
  color: #1B4464;
}

.link-text {
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

/* Seção de fatores clínicos */
.fatores-clinicos-section {
  width: 100%;
}

.fatores-info-card {
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #E8ECF0;
  margin: 0 1rem;
}

.fatores-info-header {
  text-align: center;
  margin-bottom: 1rem;
}

.fatores-info-icon {
  color: #1B4464;
  font-size: 1.5rem;
}

.conditions-container-small {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 1rem;
  max-width: 450px;
  margin-left: auto;
  margin-right: auto;
}

.condition-small {
  width: 100%;
  font-size: 8pt;
}

.condition-small .card {
  height: 100%;
}

.condition-small .card-header {
  border-bottom: 1px solid #eee;
}

.condition-small .card-body {
  background: linear-gradient(to bottom, #fafafa, #f2f2f2);
}

.option-image-container-small {
  height: 130px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.option-image-container-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Seção de mentoria */
.mentoria-section {
  width: 100%;
}

.mentoria-section-bottom {
  width: 100%;
  text-align: center;
}

.mentoria-info-card-centered {
  background: linear-gradient(145deg, #f8f9fa, #ffffff);
  border-radius: 12px;
  padding: 1.2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #E8ECF0;
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.mentoria-section-no-treatment {
  width: 100%;
}

.mentoria-info-card {
  background: linear-gradient(145deg, #f8f9fa, #ffffff);
  border-radius: 12px;
  padding: 1.2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #E8ECF0;
}

.mentoria-info {
  margin-bottom: 1rem;
}

.mentoria-text {
  width: 100%;
}

.mentoria-description {
  color: #495057;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.6;
  text-align: justify;
}

.mentoria-action {
  text-align: center;
}

.btn-mentoria {
  background: linear-gradient(135deg, #1B4464, #56809F);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(27, 68, 100, 0.2);
  cursor: pointer;
}

.btn-mentoria:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(27, 68, 100, 0.3);
  background: linear-gradient(135deg, #28597e, #7aa3c0);
}

.mentoria-solicitada {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
}

/* Estilos para os cards de fatores clínicos */
.conditions-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  grid-row-gap: 20px;
  margin-top: 1rem;
}

.condition {
  max-width: 190px;
  font-size: 8pt;
}

.condition .card {
  height: 100%;
}

.condition .card-header {
  border-bottom: 1px solid #eee;
}

.condition .card-body {
  background: linear-gradient(to bottom, #fafafa, #f2f2f2);
}

/* Container dos botões de prognóstico */
.prognostico-buttons-container {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.btn-prognostico-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Setinha do prognóstico selecionado */
.prognostico-arrow {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  z-index: 10;
  animation: arrowPulse 2s ease-in-out infinite;
}

.arrow-desfavoravel {
  border-top: 10px solid #dc3545;
}

.arrow-duvidoso {
  border-top: 10px solid #495057;
}

.arrow-favoravel {
  border-top: 10px solid #198754;
}

@keyframes arrowPulse {
  0%, 100% {
    opacity: 0.8;
    transform: translateX(-50%) translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(-2px);
  }
}

/* Estilos para os botões de prognóstico */
.btn-prognostico {
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border: 2px solid #E8ECF0;
  color: rgb(128, 136, 143);
  border-radius: 12px;
  width: 170px;
  height: 85px;
  font-weight: 600;
  font-size: 13pt;
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;
  padding: 0;
  margin-bottom: 0px !important;
  text-transform: uppercase;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.btn-prognostico::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.btn-prognostico:hover::before {
  left: 100%;
}

.btn-prognostico.active {
  background: linear-gradient(145deg, #ffffff, #f0f0f0);
  border-width: 3px;
  border-style: solid;
  transform: scale(1.03);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.btn-prognostico.active:hover {
  cursor: default;
  transform: scale(1.03);
}

.btn-prognostico:not(.active):hover {
  transform: scale(1.04);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.15);
  border-color: #D0D7DE;
}

.btn-prognostico.btn-desfavoravel.active {
  border-color: #dc3545;
  color: #dc3545;
  background: linear-gradient(145deg, #fff5f5, #ffeaea);
}

.btn-prognostico.btn-duvidoso.active {
  border-color: #495057;
  color: #495057;
  background: linear-gradient(145deg, #f8f9fa, #e9ecef);
}

.btn-prognostico.btn-favoravel.active {
  border-color: #198754;
  color: #198754;
  background: linear-gradient(145deg, #f0fff4, #e6fffa);
}

.btn-prognostico.btn-desfavoravel:not(.active):hover {
  border-color: rgba(220, 53, 69, 0.6);
  color: #dc3545;
  background: linear-gradient(145deg, #fff8f8, #fff0f0);
}

.btn-prognostico.btn-duvidoso:not(.active):hover {
  border-color: rgba(73, 80, 87, 0.6);
  color: #495057;
  background: linear-gradient(145deg, #fafbfc, #f1f3f4);
}

.btn-prognostico.btn-favoravel:not(.active):hover {
  border-color: rgba(25, 135, 84, 0.6);
  color: #198754;
  background: linear-gradient(145deg, #f8fff8, #f0fff0);
}

.next-btn-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Estilos para o modal de mentoria */
.mentoria-modal {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.mentoria-header {
  background: linear-gradient(135deg, #1B4464, #56809F);
  border-bottom: none;
  padding: 15px 20px;
}

.mentoria-header .modal-title {
  color: white;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.mentoria-header .btn-close {
  color: white;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.mentoria-header .btn-close:hover {
  opacity: 1;
  transform: scale(1.1);
}

.mentoria-body {
  padding: 20px;
  background-color: #f8f9fa;
}

.mentoria-paciente {
  font-size: 1.1rem;
  color: #1B4464;
  margin-bottom: 15px;
}

.mentoria-info {
  color: #495057;
  line-height: 1.5;
  margin-bottom: 15px;
}

.mentoria-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 10px;
}

.mentoria-textarea {
  border-radius: 8px;
  border: 1px solid #E0E5EB;
  padding: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.mentoria-textarea:focus {
  border-color: #80A1BB;
  box-shadow: 0 0 0 3px rgba(27, 68, 100, 0.1);
}

.mentoria-footer {
  border-top: 1px solid #E0E5EB;
  padding: 15px 20px;
  background-color: #f8f9fa;
}

.mentoria-btn {
  padding: 8px 20px;
  font-weight: 500;
  border-radius: 6px;
  box-shadow: 0 3px 6px rgba(27, 68, 100, 0.15);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #1B4464, #56809F);
  border: none;
}

.mentoria-btn:hover {
  box-shadow: 0 4px 8px rgba(27, 68, 100, 0.25);
  transform: translateY(-1px);
  background: linear-gradient(135deg, #28597e, #7aa3c0);
}

/* Estilos para o card de prognóstico */
.prognostico-card {
  background: linear-gradient(145deg, #f8f9fa, #ffffff);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 1px solid #E8ECF0;
  overflow: hidden;
}

.prognostico-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.prognostico-container {
  padding: 2rem 1.5rem;
  border-radius: 6px;
  margin: 0 auto;
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  text-align: center;
  position: relative;
}

.prognostico-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(135deg, #1B4464, #56809F);
  border-radius: 0 0 4px 4px;
}

.prognostico-info {
  font-size: 1.1rem;
  color: #1B4464;
  margin-bottom: 25px;
  min-height: 2.5em;
  line-height: 1.5;
  font-weight: 500;
  padding: 0 1rem;
}

/* Estilos para os botões de solicitar mentoria */
.solicitar-mentoria-btn {
  padding: 8px 20px;
  font-weight: 500;
  border-radius: 6px;
  box-shadow: 0 3px 6px rgba(27, 68, 100, 0.15);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #1B4464, #56809F);
  border: none;
}

.solicitar-mentoria-btn:hover {
  box-shadow: 0 4px 8px rgba(27, 68, 100, 0.25);
  transform: translateY(-1px);
  background: linear-gradient(135deg, #28597e, #7aa3c0);
}

.mentoria-solicitada-btn {
  padding: 8px 20px;
  font-weight: 500;
  border-radius: 6px;
  box-shadow: 0 3px 6px rgba(76, 175, 80, 0.15);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  border: none;
}

/* Responsividade */
@media (max-width: 768px) {
  .diagnostico-items-grid {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .diagnostico-item {
    max-width: 100%;
    font-size: 0.8rem;
    padding: 6px 8px;
  }

  .diagnostico-icon {
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    margin-right: 6px;
  }

  .observacoes-section {
    padding: 1rem;
    margin-top: 1rem;
  }

  .protocolo-content {
    flex-direction: column;
  }

  .tratamento-column {
    justify-content: center;
    margin-bottom: 1.5rem;
    min-height: auto;
  }

  .informacoes-column {
    gap: 1rem;
    min-height: auto;
    justify-content: flex-start;
  }

  .informacoes-column > * {
    flex: none;
  }

  .tratamento-recomendado-section {
    max-width: 100%;
  }

  .tratamento-frame {
    max-width: 100%;
    padding: 1rem;
  }

  .prognostico-buttons-container {
    gap: 1rem;
  }

  .btn-prognostico {
    width: 140px;
    height: 75px;
    font-size: 11pt;
  }

  .section-title {
    font-size: 1rem;
  }

  .mentoria-info {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }

  .mentoria-icon {
    font-size: 1.2rem;
    margin-top: 0;
  }
}

@media (max-width: 576px) {
  .diagnostico-container {
    padding: 1rem;
  }

  .observacoes-section {
    padding: 0.8rem;
  }

  .prognostico-buttons-container {
    gap: 0.8rem;
  }

  .btn-prognostico {
    width: 120px;
    height: 70px;
    font-size: 10pt;
  }

  .tratamento-frame {
    padding: 0.8rem;
  }

  .section-header {
    flex-direction: column;
    gap: 5px;
  }

  .section-icon {
    font-size: 1.1rem;
  }

  .section-title {
    font-size: 0.9rem;
  }
}
</style>

<script>
import { diagnosticoImage } from "@/helpers/diagnosticoImage.js";
import { salvarDiagnostico, salvarPrognostico } from "@/services/pacientesService";
import { solicitarMentoria } from "@/services/mentoriasService";
import cSwal from "@/utils/cSwal.js";

var isEditing = [];

export default {
  name: "Diagnostico",
  props: {
    diagnostico: {
      type: String,
      default: "",
    },
    prognostico: {
      type: String,
      default: "",
    },
    paciente: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  emits: ["selectTab"],
  data() {
    return {
      observacoesMentoria: "",
      diagnostico_: "",
      prognostico_: "",
      isEditing,
      mentoriaSolicitada: false,
      imagePaths: {}, // Para armazenar os caminhos das imagens
    };
  },
  methods: {
    async loadImages() {
      // Inicializa imagePaths como um objeto reativo
      this.imagePaths = {};

      // Carrega imagens dos fatores clínicos
      if (this.paciente.fatores_clinicos) {
        for (const fator of this.fatoresClinicosFiltrados) {
          this.imagePaths[fator.tag] = await diagnosticoImage('fatorClinico', fator.tag);
        }
      }

      // Carrega imagem do tratamento sugerido
      if (this.paciente.tratamentos_sugeridos && this.paciente.tratamentos_sugeridos.length > 0) {
        const tratamento = this.paciente.tratamentos_sugeridos[0];
        this.imagePaths[tratamento.tag] = await diagnosticoImage('tratamentoSugerido', tratamento.tag);
      }
    },
    gerarPlanoTratamento() {
      this.$emit("selectTab", "planoTratamento");
    },
    goToAnalise() {
      this.$emit("selectTab", "analise");
    },
    confirmSolicitarMentoria() {
      cSwal.cConfirm(
        "Deseja realmente solicitar mentoria para este paciente?",
        async () => {
          const save = await solicitarMentoria(
            this.paciente.id,
            this.observacoesMentoria
          );

          if (save) {
            cSwal.cSuccess("A mentoria foi solicitada. Em breve você receberá uma resposta de um de nossos especialistas.", { timer: 4000 });
            this.isEditing["diagnostico"] = false;
            this.mentoriaSolicitada = true;
            this.$emit("pacienteChange");
          } else cSwal.cError("Ocorreu um erro ao abrir solicitação.");
        }
      );
    },
    confirmSalvarDiagnostico() {
      cSwal.cConfirm(
        "Deseja realmente salvar as alterações no diagnóstico?",
        async () => {
          const save = await salvarDiagnostico(this.paciente.id, this.diagnostico_);

          if (save) {
            cSwal.cSuccess("As alterações foram salvas com sucesso.");
            this.isEditing["diagnostico"] = false;
            this.$emit("pacienteChange");
          } else cSwal.cError("Ocorreu um erro ao salvar as alterações");
        }
      );
    },
    confirmSalvarPrognostico(novoPrognostico) {
      cSwal.cConfirm(
        `Deseja definir o prognóstico do paciente como <b>${novoPrognostico.toUpperCase()}</b>?`,
        async () => {
          cSwal.loading("Salvando prognóstico...");
          const save = await salvarPrognostico(this.paciente.id, novoPrognostico);
          cSwal.loaded();

          if (save) {
            cSwal.cSuccess("O prognóstico foi definido.");
            this.prognostico_ = novoPrognostico;
            this.$emit("pacienteChange");
          } else cSwal.cError("Ocorreu um erro ao alterar o prognóstico.");
        }
      );
    },
    handlePrognosticoClick(novoPrognostico) {
      if (this.prognostico_ !== novoPrognostico) {
        this.confirmSalvarPrognostico(novoPrognostico);
      }
    },
    toggleEditMode(section) {
      this.isEditing[section] = !this.isEditing[section];
    },
    getPropsModels() {
      this.diagnostico_ = this.diagnostico;
      this.prognostico_ = this.prognostico;
    },
  },
  components: {},
  computed: {
    fatoresClinicosFiltrados() {
      return this.paciente.fatores_clinicos.filter((fator) =>
        JSON.parse(this.paciente.fatores_considerados).includes(fator.tag)
      );
    },
    diagnosticoModel: {
      get() {
        return this.diagnostico;
      },
      set(value) {
        this.$emit("update:diagnostico", value);
      },
    },
    prognosticoModel: {
      get() {
        return this.prognostico;
      },
      set(value) {
        this.$emit("update:prognostico", value);
      },
    },
  },
  created() {
    this.getPropsModels();
  },
  mounted() {
    this.loadImages();
    this.mentoriaSolicitada = this.paciente.mentoria;
  },
  beforeMount() {},
  beforeUnmount() {},
};
</script>
